import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewChild,
  computed,
  signal,
  effect,
  DestroyRef
} from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { TranslocoDirective, TranslocoService } from '@jsverse/transloco'
import { EcomuiTableModule } from '@ft/ecom-ui2/table'
import { EcomUiCheckboxModule } from '@ft/ecom-ui2/checkbox'
import {
  CdkCell,
  CdkCellDef,
  CdkColumnDef,
  CdkHeaderCell,
  CdkHeaderCellDef,
  CdkHeaderRow,
  CdkHeaderRowDef,
  CdkRow,
  CdkRowDef,
  CdkTable
} from '@angular/cdk/table'
import { ColumnStyleWidthDirective } from '../../../directives/columnStyleWidth.directive'
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling'
import { CdkTableVirtualScrollDataSource, TableVirtualScrollModule } from 'ng-table-virtual-scroll'
import {
  AssortmentClusterSquareConfigurationReadModel,
  AssortmentClusterSquareConfigurationWriteModel,
  AssortmentReadModel,
  AssortmentUpdateWriteModel,
  ClusterReadModel,
  ClusterSquareReadModel
} from '@ft/data-access-assortment'
import { FindEktPipe } from '../../../pipes/find-ekt/find-ekt.pipe'
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { EcomuiTooltipModule } from '@ft/ecom-ui2/tooltip'
import { DropDownComponent, Selected } from '../../drop-down/drop-down.component'
import { EcomuiCdkModule } from '@ft/ecom-ui-cdk'
import { EcomUiContextMenuModule } from '@ft/ecom-ui2/context-menu'
import { EcomUiButtonModule } from '@ft/ecom-ui2/button'
import { GridSettingsService, ExtendedFieldsSettingsService } from '@ft/ecom-ui2/grid-settings'
import { RxState } from '@rx-angular/state'
import { combineLatest, of, debounceTime, distinctUntilChanged } from 'rxjs'
import { map, startWith } from 'rxjs/operators'
import { EcomuiFormFieldModule } from '@ft/ecom-ui2/form-field'
import { EcomuiSelectModule } from '@ft/ecom-ui2/select'
import {
  Cluster,
  NewState,
  ColumnsConfig,
  UpdateCluster,
  UpdateSquare,
  HeaderConfig,
  ItemTypeColumn,
  ItemEditTableKey
} from '../../../models/table-assortment.model'
import { TableEditorService } from '../../../services/table-editor.service'
import { LocalStorageService } from '@ft/shared-platform'
import { ResizeColTableDirective } from '../../../directives/resizeColTable.directive'
import { AsyncPipe } from '@angular/common'

interface State {
  total: number
  displayedColumns: string[]
  dataTable: CdkTableVirtualScrollDataSource<AssortmentReadModel>
  locoDropDown: Selected[]
  deliveryType: Selected[]
  sticky: boolean
  columnsConfig: ColumnsConfig[]
  loading: boolean
}

// Константы для оптимизации
const DEBOUNCE_TIME = 300
const VIRTUAL_SCROLL_ITEM_SIZE = 60
const HEADER_HEIGHT = 120
const PAGE_SIZE = 50

const header = [
  {
    color: 'rgb(246 248 253)',
    type: 'basic',
    colspan: 8,
    header: 1,
    sticky: true,
    column: 'group1',
    squares: [
      {
        colspan: 8,
        sticky: true,
        type: 'basic',
        header: 2,
        column: 'area1',
        title: '',
        color: 'rgb(246 248 253)'
      }
    ],
    title: 'Базові параметри'
  }
]

const GRID_SETTINGS_LS_KEY = 'assortmentGridSettings'
const GRID_SETTINGS_EXTRA_PARAMETERS_KEY = 'assortmentExtraParametersSettings'
const WIDTH_COLUM_CACHE = 'assortmentWidthColum'

const width = {
  productCategoryCode: 56,
  productCategory: 72,
  itemNumber: 80,
  productName: 100,
  unit: 80,
  need: 80,
  csb: 56,
  loco: 96,
  newState: 120,
  factState: 120,
  deliveryType: 120
}

@Component({
  selector: 'ft-assortment-table-assortment',
  templateUrl: './table-assortment.component.html',
  styleUrls: ['./table-assortment.component.scss', './../../main.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TranslocoDirective,
    EcomuiTableModule,
    EcomUiCheckboxModule,
    CdkCell,
    CdkCellDef,
    CdkColumnDef,
    CdkHeaderCell,
    CdkHeaderCellDef,
    CdkHeaderRow,
    CdkHeaderRowDef,
    CdkRow,
    CdkRowDef,
    CdkTable,
    ColumnStyleWidthDirective,
    CdkVirtualScrollViewport,
    TableVirtualScrollModule,
    EcomuiTooltipModule,
    FindEktPipe,
    DropDownComponent,
    ReactiveFormsModule,
    EcomuiCdkModule,
    EcomUiContextMenuModule,
    EcomUiButtonModule,
    EcomuiFormFieldModule,
    EcomuiSelectModule,
    ResizeColTableDirective,
    AsyncPipe
  ],
  standalone: true
})
export class TableAssortmentComponent extends RxState<State> implements OnInit {
  private readonly gridSettingsService = inject(GridSettingsService)
  private readonly extendedFieldsSettingsService = inject(ExtendedFieldsSettingsService)
  private readonly localStorageService = inject(LocalStorageService)
  private readonly translocoService = inject(TranslocoService)
  private readonly tableEditor = inject(TableEditorService)
  private readonly destroyRef = inject(DestroyRef)

  // Сигналы для оптимизации реактивности
  protected readonly clusters = signal<Cluster | null>(null)
  protected readonly totalItem = signal<number>(0)
  protected readonly page = signal<number>(1)
  protected readonly loading = signal<boolean>(false)

  // Computed сигналы для производных данных
  protected readonly canLoadMore = computed(() => {
    const total = this.totalItem()
    const currentPage = this.page()
    return currentPage < Math.ceil(total / PAGE_SIZE)
  })

  @Input() set getDataTable({ data, clusters }: { data: AssortmentReadModel[]; clusters: Cluster }) {
    this.clusters.set(clusters)
    if (!this.tableFormGroup) {
      this.createFormGroup()
      this.updateDataClusters()
    }
    if (data) {
      this.loading.set(true)
      const updatedData = this.get('dataTable')?.data || []
      this.changeVirtualScroll([...updatedData, ...data])
      this.createIndex()
      this.createLocoControls(data)
      this.createFormArrayControl(this.updateClusters, this.get('dataTable').data)
      this.generateTableColumns()
      this.loading.set(false)
    }
  }

  @Input() set filterCluster(clusters: Cluster) {
    if (clusters) {
      this.resetTable(clusters)
    }
  }

  @Input() set totalItemInput(value: number) {
    this.totalItem.set(value)
  }

  @Output() eventEndScroll = new EventEmitter<number>()
  @ViewChild(CdkVirtualScrollViewport) viewport!: CdkVirtualScrollViewport

  public state$ = this.select()
  headerTable: HeaderConfig[] = header
  displayedColumnsLevel1 = ['group1']
  displayedColumnsLevel2: string[] = ['area1']
  displayedColumnsLevel3: string[] = []
  displayedColumnsLevel3Original = [
    'productCategoryCode',
    'productCategory',
    'itemNumber',
    'productName',
    'unit',
    'need',
    'csb',
    'loco'
  ]
  statArray = ['factState', 'newState', 'deliveryType']
  controls = new FormArray<FormControl<any>>([])
  tableFormGroup!: FormGroup
  headerConfig: HeaderConfig[] = header
  levelControl = new FormControl(3)
  updateClusters: UpdateCluster[] = []
  changedCells: { value: boolean; column: string; index: number }[] = []
  time = 0
  minWidth: any = width
  changeWidth!: any
  horizonScroll!: number
  initialWidthSet = false
  borderColor = 'rgb(227 227 234)'
  gridColumns: string[] = []
  extendedColumns: string[] = []
  itemTypeColumn = ItemTypeColumn

  ngOnInit(): void {
    this.setState()
    this.generateTableHeader()
    this.updateDisplayedColumns()
    this.getNewWidth()
    this.setupFormChangeHandling()
  }

  private setupFormChangeHandling(): void {
    // Оптимизированная обработка изменений формы с debounce
    effect(() => {
      if (this.tableFormGroup) {
        this.tableFormGroup.valueChanges
          .pipe(
            debounceTime(DEBOUNCE_TIME),
            distinctUntilChanged(),
            takeUntilDestroyed(this.destroyRef)
          )
          .subscribe(() => {
            this.handleFormChanges()
          })
      }
    })
  }

  private handleFormChanges(): void {
    // Логика обработки изменений формы
    if (this.tableFormGroup.dirty) {
      // Обработка изменений
    }
  }

  listenToHorizontalScroll(): void {
    if (this.viewport) {
      const viewportElement = this.viewport.elementRef.nativeElement as HTMLElement
      this.horizonScroll = viewportElement.scrollLeft
    }
  }

  onToggleSticky(sticky: boolean): void {
    this.set({ sticky })
    if (!sticky) {
      this.resetHorizontalScroll()
    }
  }

  resetHorizontalScroll(): void {
    const viewport = this.viewport.elementRef.nativeElement as HTMLElement
    viewport.scrollLeft = 0
  }

  getNewWidth(): void {
    const getWidth = this.localStorageService.getItem(WIDTH_COLUM_CACHE)
    if (getWidth) {
      this.changeWidth = JSON.parse(getWidth)
    } else {
      this.changeWidth = this.minWidth
    }
  }

  onWidthChange({ key, width }: { key: string; width: number }): void {
    this.changeWidth = { ...this.changeWidth, [key]: width }
    this.localStorageService.setItem(WIDTH_COLUM_CACHE, JSON.stringify(this.changeWidth))
  }

  updateDataClusters(): void {
    const clustersValue = this.clusters()
    if (!clustersValue?.items) {
      this.updateClusters = []
      return
    }

    let squareCounter = 2
    this.updateClusters = clustersValue.items.flatMap((cluster: ClusterReadModel, index: number) => {
      const squares = cluster.squares?.map((item: ClusterSquareReadModel) => {
        return {
          ...item,
          column: `area${squareCounter++}`,
          color: cluster.color,
          colspan: this.statArray.length,
          header: 2
        }
      })
      return {
        ...cluster,
        column: `group${index + 2}`,
        squares,
        colspan: (cluster?.squares?.length || 0) * this.statArray.length,
        header: 1,
        total: squares?.length
      }
    })
  }

  createFormGroup(): void {
    this.tableFormGroup = new FormGroup({
      newState: new FormGroup({}),
      deliveryType: new FormGroup({}),
      loco: new FormArray([])
    })
  }

  createFormArrayControl(data: UpdateCluster[], tableData: AssortmentReadModel[]): void {
    if (!data || !tableData.length) return

    const newStateGroup = this.tableFormGroup.get('newState') as FormGroup
    const deliveryTypeGroup = this.tableFormGroup.get('deliveryType') as FormGroup

    // Оптимизация: создаем Map для быстрого поиска конфигураций
    const configMap = new Map<string, AssortmentClusterSquareConfigurationReadModel>()
    tableData.forEach(product => {
      product.clusterSquareConfigurations?.forEach(config => {
        const key = `${config.clusterId}_${config.squareId}_${product.id}`
        configMap.set(key, config)
      })
    })

    data.forEach((cluster: UpdateCluster) => {
      if (!cluster.squares) return

      cluster.squares.forEach((square: UpdateSquare) => {
        let newStateArray = newStateGroup.get(square.column) as FormArray<FormGroup>
        let deliveryTypeArray = deliveryTypeGroup.get(square.column) as FormArray

        if (!newStateArray) {
          newStateArray = new FormArray<FormGroup>([])
          newStateGroup.addControl(square.column, newStateArray)
        }

        if (!deliveryTypeArray) {
          deliveryTypeArray = new FormArray<FormControl>([])
          deliveryTypeGroup.addControl(square.column, deliveryTypeArray)
        }

        // Batch создание контролов для лучшей производительности
        const newStateControls: FormGroup[] = []
        const deliveryTypeControls: FormControl[] = []

        tableData.forEach((product: AssortmentReadModel) => {
          const configKey = `${cluster.id}_${square.id}_${product.id}`
          const setting = configMap.get(configKey)

          const form = new FormGroup({
            id: new FormControl(configKey),
            readOnlyValue: new FormControl({ value: setting?.isOpenForSale ?? false, disabled: true }),
            editableValue: new FormControl(setting?.isOpenForSale ?? false)
          })

          newStateControls.push(form)

          const deliveryType = setting?.deliveryType
          deliveryTypeControls.push(
            new FormControl({
              id: deliveryType === 'supply' ? '1' : '2',
              name: deliveryType || 'empty'
            })
          )
        })

        // Batch добавление контролов
        newStateControls.forEach(control => newStateArray.push(control))
        deliveryTypeControls.forEach(control => deliveryTypeArray.push(control))
      })
    })
  }

  createLocoControls(data: AssortmentReadModel[]): void {
    const locoArray = this.tableFormGroup.get('loco') as FormArray

    data.forEach((row: AssortmentReadModel) => {
      const locoControl = new FormControl({
        id: row.enabledForLoko ? '1' : '2',
        name: row.enabledForLoko,
        parentId: row.id
      })
      locoArray.push(locoControl)
    })
  }

  updateDisplayedColumns(): void {
    this.connect(
      'displayedColumns',
      combineLatest([
        this.gridSettingsService.select('displayedSettings'),
        of(this.displayedColumnsLevel3),
        this.extendedFieldsSettingsService.select('displayedSettings')
      ]).pipe(
        debounceTime(100), // Добавляем debounce для оптимизации
        distinctUntilChanged((prev, curr) => {
          // Оптимизация: сравниваем только если действительно изменились данные
          return JSON.stringify(prev) === JSON.stringify(curr)
        }),
        map(([gridColumns, level3Columns, extendedColumns]) => {
          this.time = 50

          // Оптимизация: кешируем вычисления
          const clusters = this.updateClusters
            .flatMap((item: any) => item.squares)
            .map((square: UpdateSquare) => square.column)

          this.updateColspan(extendedColumns.length, gridColumns.length)

          // Оптимизация: используем более эффективный способ создания массива
          const orderedColumns = clusters.flatMap((area: string) =>
            extendedColumns.map((field: string) => `${area}${field}`)
          )

          // Проверка изменений для сброса ширины колонок
          if (this.initialWidthSet) {
            if (
              this.gridColumns.length !== gridColumns.length ||
              this.extendedColumns.length !== extendedColumns.length
            ) {
              this.resetColumnWidth()
            }
          }
          this.initialWidthSet = true

          // Оптимизация: используем Set для быстрого поиска
          const level3Set = new Set(level3Columns)
          const filtered = orderedColumns.filter((col: string) => level3Set.has(col))

          this.gridColumns = gridColumns
          this.extendedColumns = extendedColumns
          return [...gridColumns, ...filtered]
        })
      )
    )
  }

  resetColumnWidth(): void {
    this.changeWidth = width
    this.localStorageService.setItem(WIDTH_COLUM_CACHE, JSON.stringify(this.changeWidth))
  }

  updateColspan(length: number, lengthSticky: number): void {
    const colspanDelta = this.statArray.length - length
    const colspanSticky = this.displayedColumnsLevel3Original.length - lengthSticky

    this.headerConfig = this.headerTable.map((el: HeaderConfig) => {
      const newColspan =
        el.header === 1
          ? el.colspan - (el.column !== 'group1' ? colspanDelta * (el.squares?.length ?? 0) : colspanSticky)
          : el.header === 2
            ? el.colspan - (el.column !== 'area1' ? colspanDelta : colspanSticky)
            : el.colspan

      return { ...el, colspan: newColspan }
    })
  }

  setState(): void {
    this.gridSettingsService.initSettingsCfg(this.displayedColumnsLevel3Original, GRID_SETTINGS_LS_KEY)
    this.extendedFieldsSettingsService.initSettingsCfg(this.statArray, GRID_SETTINGS_EXTRA_PARAMETERS_KEY)
    this.set({
      locoDropDown: [
        { id: '1', name: true },
        { id: '2', name: false }
      ],
      deliveryType: [
        { id: '1', name: 'supply' },
        { id: '2', name: 'replenish' },
        { id: '3', name: 'empty' }
      ],
      sticky: true
    })
  }

  scrollBottom(): void {
    const totalPage = Math.ceil(this.totalItem() / PAGE_SIZE)
    const currentPage = this.page()
    if (currentPage < totalPage) {
      const newPage = currentPage + 1
      this.page.set(newPage)
      this.eventEndScroll.emit(newPage)
    }
  }

  onItemsChange(event: any, type: string, element: AssortmentReadModel, item?: ColumnsConfig): void {
    if (!this.tableFormGroup.dirty) return

    // Оптимизация: используем более эффективную обработку изменений
    try {
      switch (type) {
        case ItemEditTableKey.EnabledForLoko:
          this.updateEnabledForLoko(element, event)
          break
        case ItemEditTableKey.DeliveryType:
        case ItemEditTableKey.IsOpenForSale:
          if (item) {
            this.updateClusterSquareConfiguration(element, item, type, event)
          }
          break
        default:
          console.warn(`Unknown item type: ${type}`)
          return
      }

      // Batch обновление для лучшей производительности
      this.tableEditor.setItem(element as AssortmentUpdateWriteModel)
    } catch (error) {
      console.error('Error updating item:', error)
    }
  }

  private updateEnabledForLoko(element: AssortmentReadModel, event: any): void {
    element.enabledForLoko = event?.name ?? false
  }
  
  private updateClusterSquareConfiguration(
    element: AssortmentReadModel,
    item: ColumnsConfig,
    type: string,
    event: any
  ): void {
    // Оптимизация: инициализируем массив если его нет
    if (!element.clusterSquareConfigurations) {
      element.clusterSquareConfigurations = []
    }

    const existingConfig = element.clusterSquareConfigurations.find(
      (csq) => csq.clusterId === item.idCluster && csq.squareId === item.squareId
    )

    if (existingConfig) {
      this.updateExistingConfig(existingConfig, type, event)
    } else {
      this.createNewConfig(element, item, type, event)
    }
  }

  private updateExistingConfig(config: AssortmentClusterSquareConfigurationReadModel, type: string, event: any): void {
    // Оптимизация: используем более строгую типизацию и проверки
    switch (type) {
      case ItemEditTableKey.DeliveryType:
        config.deliveryType = event?.name === 'empty' ? null : (event?.name || null)
        break
      case ItemEditTableKey.IsOpenForSale:
        config.isOpenForSale = Boolean(event)
        break
      default:
        console.warn(`Unknown config type: ${type}`)
    }
  }

  private createNewConfig(
    element: AssortmentReadModel,
    item: ColumnsConfig,
    type: string,
    event: any
  ): void {
    const newConfig: AssortmentClusterSquareConfigurationWriteModel = {
      clusterId: item.idCluster,
      squareId: item.squareId!,
      deliveryType: type === ItemEditTableKey.DeliveryType
        ? (event?.name === 'empty' ? null : (event?.name || null))
        : null,
      isOpenForSale: type === ItemEditTableKey.IsOpenForSale ? Boolean(event) : false
    }

    // Оптимизация: безопасное добавление в массив
    const configs = element.clusterSquareConfigurations as AssortmentClusterSquareConfigurationWriteModel[]
    configs.push(newConfig)
  }

  changeNewState(value: boolean, column: string, index: number): void {
    if (!this.tableFormGroup.dirty) return

    // Оптимизация: используем Map для быстрого поиска
    const cellKey = `${column}_${index}`
    const existingCellIndex = this.changedCells.findIndex(
      (cell) => `${cell.column}_${cell.index}` === cellKey
    )

    if (existingCellIndex !== -1) {
      this.changedCells[existingCellIndex].value = value
    } else {
      this.changedCells.push({ value, column, index })
    }
  }

  createIndex(): void {
    // Оптимизация: избегаем мутации исходных данных
    const dataTable = this.get('dataTable')
    if (dataTable?.data) {
      dataTable.data = dataTable.data.map((product: AssortmentReadModel, index: number) => ({
        ...product,
        index
      }))
    }
  }

  changeVirtualScroll(data: AssortmentReadModel[]): void {
    // Оптимизация: проверяем данные перед созданием источника
    if (!data || !Array.isArray(data)) {
      console.warn('Invalid data provided to changeVirtualScroll')
      return
    }

    const dataSource = new CdkTableVirtualScrollDataSource(data)
    this.set({ dataTable: dataSource })
  }

  // Новые оптимизированные методы
  private optimizeFormPerformance(): void {
    // Отключаем валидацию для лучшей производительности при массовых изменениях
    if (this.tableFormGroup) {
      this.tableFormGroup.updateValueAndValidity({ emitEvent: false })
    }
  }

  private batchUpdateCells(updates: { value: boolean; column: string; index: number }[]): void {
    // Batch обновление для лучшей производительности
    updates.forEach(update => {
      this.changeNewState(update.value, update.column, update.index)
    })
    this.optimizeFormPerformance()
  }

  trackByFn(_index: number, item: AssortmentReadModel): string {
    return item.id
  }

  private generateTableHeader(): void {
    const clusters = this.updateClusters
    const headerSource = [...this.headerConfig, ...clusters]
    this.headerTable = headerSource.flatMap((header) => {
      const updateHeader = {
        ...header,
        borderColor:
          header?.type === 'basic'
            ? this.borderColor
            : header?.color
              ? this.darkenColor(header.color, 10)
              : this.borderColor
      }
      const updateSquares = header.squares?.map((square) => {
        return {
          ...square,
          borderColor:
            header?.type === 'basic'
              ? this.borderColor
              : header?.color
                ? this.darkenColor(header.color, 10)
                : this.borderColor
        }
      })
      return [updateHeader, ...(updateSquares ?? [])]
    }) as HeaderConfig[]
    this.displayedColumnsLevel1 = headerSource.map((header) => header.column)
    this.displayedColumnsLevel2 = headerSource.flatMap((header) =>
      header?.squares?.map((item: any) => item.column)
    ) as string[]

    const exclusiveStatArray = clusters
      .flatMap((cluster: UpdateCluster) => cluster?.squares?.map((item: UpdateSquare) => item.column) as string[])
      .flatMap((square: string) => this.statArray.map((item: string) => `${square}${item}`))

    this.displayedColumnsLevel3 = [...this.displayedColumnsLevel3Original, ...exclusiveStatArray]
  }

  private generateTableColumns(): void {
    const clusters = this.updateClusters
    const data = this.get('dataTable').data

    if (!clusters.length || !data.length) {
      this.set({ columnsConfig: [] })
      return
    }

    // Оптимизация: создаем Map для быстрого доступа к настройкам
    const settingsMap = new Map<string, AssortmentClusterSquareConfigurationReadModel[]>()

    // Batch обработка для лучшей производительности
    data.forEach((product: AssortmentReadModel) => {
      product.clusterSquareConfigurations?.forEach((clusterSquare: AssortmentClusterSquareConfigurationReadModel) => {
        const key = `${clusterSquare.clusterId}_${clusterSquare.squareId}`
        const existing = settingsMap.get(key)
        if (existing) {
          existing.push(clusterSquare)
        } else {
          settingsMap.set(key, [clusterSquare])
        }
      })
    })

    // Оптимизация: предварительно кешируем переводы
    const translationCache = new Map<string, string>()
    this.statArray.forEach(item => {
      translationCache.set(item, this.translocoService.translate(`assortmentBackOffice.settings.${item}`))
    })

    const columnsConfig = clusters.flatMap((cluster: UpdateCluster) => {
      const squares = cluster?.squares || []
      const lastSquareIndex = squares.length - 1

      return squares.flatMap((square: UpdateSquare, squareIndex: number) => {
        const settingKey = `${cluster.id}_${square.id}`
        const foundSettings = settingsMap.get(settingKey) || []

        return this.statArray.map((item: string, itemIndex: number, arr: string[]) => ({
          idColum: `${square.column}${item}`,
          idCluster: cluster.id,
          squareId: square.id,
          color: square.color,
          type: item,
          borderColor: squareIndex === lastSquareIndex && itemIndex === arr.length - 1
            ? this.darkenColor(square.color, 10)
            : '',
          text: translationCache.get(item) || item,
          column: square.column,
          remains: item.includes('remains') ? foundSettings.map((el: any) => el.remains) : []
        }))
      })
    })

    this.set({ columnsConfig })
  }

  get getLoco(): FormArray<FormControl> {
    return this.tableFormGroup.get('loco') as FormArray
  }

  changeForm(): void {
    const newStateGroup = this.tableFormGroup.get('newState') as FormGroup
    this.changedCells.forEach(({ column, index }: NewState) => {
      const control = (newStateGroup?.get(column) as FormArray).at(index) as FormGroup
      control?.get('readOnlyValue')?.setValue(control.get('editableValue')?.value)
    })
  }

  get newState() {
    return this.tableFormGroup.get('newState') as any
  }

  get deliveryType() {
    return this.tableFormGroup.get('deliveryType') as any
  }

  private darkenColor(hex: string, percent: number): string {
    let r = parseInt(hex.slice(1, 3), 16)
    let g = parseInt(hex.slice(3, 5), 16)
    let b = parseInt(hex.slice(5, 7), 16)

    r = Math.max(0, r - (r * percent) / 100)
    g = Math.max(0, g - (g * percent) / 100)
    b = Math.max(0, b - (b * percent) / 100)

    return `rgb(${r}, ${g}, ${b})`
  }

  private resetTable(clusters: Cluster): void {
    this.clusters.set(clusters)
    this.updateClusters = []
    this.headerTable = header
    this.headerConfig = header
    this.displayedColumnsLevel1 = []
    this.displayedColumnsLevel2 = []
    this.displayedColumnsLevel3 = []
    this.set({ displayedColumns: [], columnsConfig: [] })
    this.createFormGroup()
    this.updateDataClusters()
    this.createFormArrayControl(this.updateClusters, this.get('dataTable').data)
    this.createLocoControls(this.get('dataTable').data)
    this.generateTableHeader()
    this.updateDisplayedColumns()
    this.generateTableColumns()
    this.resetHorizontalScroll()
  }
}
